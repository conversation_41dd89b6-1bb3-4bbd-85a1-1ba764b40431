# integration-service/main.py
# Uru Platform Integration Service - Third-party Service Authorization

from fastapi import FastAPI, HTTPException, Depends, status, Form, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from supabase import create_client, Client
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import asyncio
import logging
import secrets
import jwt
import os
import sys
import httpx
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from pydantic import BaseModel, Field
from cryptography.fernet import Fernet

# Add shared directory to path for environment utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))
try:
    from environment import env_config
    print(f"Environment detected: {env_config.environment.value}")
except ImportError:
    print("Environment utilities not found, using fallback configuration")
    env_config = None

# Import OAuth flow logic (copied from oauth-service)
from oauth_flow import GoogleOAuthFlow

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - start/stop background tasks"""
    # Startup
    print("Starting Uru Integration Service...")
    
    yield

    # Shutdown
    print("Shutting down Integration Service...")

# Create FastAPI app
app = FastAPI(
    title="Uru Integration Service",
    version="2025.1",
    description="Third-party service integration and authorization for Uru Platform",
    lifespan=lifespan
)

# CORS configuration
if env_config:
    allowed_origins = env_config.get_cors_origins()
else:
    allowed_origins = [
        "http://localhost:3000",
        "https://app.uruenterprises.com",
        "https://integrations.uruenterprises.com",
        "https://auth.uruenterprises.com"
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
COMPOSIO_API_KEY = os.getenv("URU_COMPOSIO_API_KEY")
COMPOSIO_BASE_URL = os.getenv("URU_COMPOSIO_BASE_URL", "https://backend.composio.dev/api")
JWT_SECRET = os.getenv("JWT_SECRET")
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_URL", "http://auth-service:8003")

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")

# Global variables for integration systems
supabase = None
google_oauth = None
integration_initialized = False

# Initialize Supabase
def initialize_supabase():
    """Initialize Supabase client with retry logic"""
    global supabase
    
    try:
        if not SUPABASE_URL or not SUPABASE_KEY:
            print("⚠️ Supabase credentials not found in environment")
            return None
            
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase client initialized successfully")
        return supabase
        
    except Exception as e:
        print(f"❌ Supabase initialization failed: {e}")
        return None

# Initialize integration systems
def initialize_integration_systems():
    """Initialize integration systems with retry logic"""
    global google_oauth

    if supabase:
        try:
            google_oauth = GoogleOAuthFlow(supabase)
            print("✅ Integration systems initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Integration system initialization failed: {e}")
            google_oauth = None
            return False
    else:
        print("⚠️ Integration systems disabled due to Supabase connection failure")
        google_oauth = None
        return False

# Initialize systems on startup
supabase = initialize_supabase()
integration_initialized = initialize_integration_systems()

# Security
security = HTTPBearer(auto_error=False)

async def validate_employee_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Validate employee token with auth-service"""
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    try:
        # Validate token with auth-service
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/auth/me",
                headers={"Authorization": f"Bearer {credentials.credentials}"}
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid authentication token"
                )
            
            return response.json()
            
    except httpx.RequestError:
        raise HTTPException(
            status_code=503,
            detail="Authentication service unavailable"
        )
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail=f"Authentication failed: {str(e)}"
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    # Check database connection
    db_status = "connected" if supabase else "disconnected"
    
    # Check integration service status
    integration_status = "enabled" if google_oauth else "disabled"
    
    # Check auth service connectivity
    auth_service_status = "unknown"
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{AUTH_SERVICE_URL}/health", timeout=5.0)
            auth_service_status = "connected" if response.status_code == 200 else "error"
    except:
        auth_service_status = "disconnected"
    
    return {
        "status": "healthy" if (supabase and google_oauth) else "degraded",
        "service": "integration-service",
        "version": "2025.1",
        "database": db_status,
        "integrations": integration_status,
        "auth_service": auth_service_status,
        "timestamp": datetime.utcnow().isoformat()
    }

# Request models
class IntegrationRequest(BaseModel):
    services: Optional[List[str]] = None
    redirect_url: Optional[str] = None

# Google OAuth Integration Endpoints
@app.post("/integrations/google/connect")
async def connect_google_services(
    request: IntegrationRequest,
    employee = Depends(validate_employee_token)
):
    """Initiate Google OAuth flow for current employee"""
    if not google_oauth:
        raise HTTPException(
            status_code=503,
            detail="Google integration service unavailable"
        )
    
    try:
        result = await google_oauth.initiate_oauth(
            employee["id"], 
            services=request.services,
            redirect_url=request.redirect_url
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Google OAuth initiation failed: {str(e)}"
        )

@app.get("/integrations/google/callback")
async def google_oauth_callback(
    code: str = Query(...),
    state: str = Query(...),
    error: Optional[str] = Query(None),
    scope: Optional[str] = Query(None)
):
    """Handle Google OAuth callback"""
    if not google_oauth:
        raise HTTPException(
            status_code=503,
            detail="Google integration service unavailable"
        )
    
    print(f"🔗 OAuth callback received:")
    print(f"   - Code length: {len(code) if code else 'None'}")
    print(f"   - State: {state[:20]}..." if state and len(state) > 20 else f"   - State: {state}")
    print(f"   - Error: {error}")
    print(f"   - Scope: {scope}")

    # If there's an OAuth error from Google, redirect immediately
    if error:
        print(f"❌ OAuth error from Google: {error}")
        frontend_url = env_config.get_frontend_url() if env_config else os.getenv("FRONTEND_URL", "https://app.uruenterprises.com")
        error_url = f"{frontend_url}/app/settings?oauth_error=true&error=" + str(error)
        return RedirectResponse(url=error_url, status_code=302)

    try:
        print("🔄 Calling google_oauth.handle_callback...")
        result = await google_oauth.handle_callback(code, state, error, scope)
        print(f"✅ OAuth callback successful: {result.get('message', 'No message')}")

        # Get the redirect URL from the result
        frontend_url = env_config.get_frontend_url() if env_config else os.getenv("FRONTEND_URL", "https://app.uruenterprises.com")
        redirect_url = result.get("redirect_url", f"{frontend_url}/app/settings")

        # Add success parameters to the redirect URL
        if "?" in redirect_url:
            redirect_url += "&oauth_success=true&provider=google"
        else:
            redirect_url += "?oauth_success=true&provider=google"

        print(f"🔄 Redirecting to: {redirect_url}")
        return RedirectResponse(url=redirect_url, status_code=302)

    except HTTPException as e:
        print(f"❌ OAuth HTTPException: {e.detail}")
        frontend_url = env_config.get_frontend_url() if env_config else os.getenv("FRONTEND_URL", "https://app.uruenterprises.com")
        error_url = f"{frontend_url}/app/settings?oauth_error=true&error=" + str(e.detail)
        return RedirectResponse(url=error_url, status_code=302)
    except Exception as e:
        print(f"❌ OAuth unexpected error: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        frontend_url = env_config.get_frontend_url() if env_config else os.getenv("FRONTEND_URL", "https://app.uruenterprises.com")
        error_url = f"{frontend_url}/app/settings?oauth_error=true&error=unexpected_error"
        return RedirectResponse(url=error_url, status_code=302)

@app.post("/integrations/google/refresh")
async def refresh_google_tokens(employee = Depends(validate_employee_token)):
    """Refresh Google OAuth tokens for current employee"""
    if not google_oauth:
        raise HTTPException(
            status_code=503,
            detail="Google integration service unavailable"
        )

    try:
        result = await google_oauth.refresh_tokens(employee["id"])
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Token refresh failed: {str(e)}"
        )

@app.delete("/integrations/google/disconnect")
async def disconnect_google_services(employee = Depends(validate_employee_token)):
    """Disconnect Google OAuth for current employee"""
    if not google_oauth:
        raise HTTPException(
            status_code=503,
            detail="Google integration service unavailable"
        )

    try:
        result = await google_oauth.disconnect_oauth(employee["id"])
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Disconnect failed: {str(e)}"
        )

@app.get("/integrations/google/status")
async def check_google_integration_status(employee = Depends(validate_employee_token)):
    """Check Google OAuth connection status for current employee"""
    if not google_oauth:
        raise HTTPException(
            status_code=503,
            detail="Google integration service unavailable"
        )

    try:
        result = await google_oauth.check_oauth_status(employee["id"])
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Status check failed: {str(e)}"
        )

@app.get("/integrations/status")
async def get_all_integration_status(employee = Depends(validate_employee_token)):
    """Get status of all integrations for current employee"""
    status = {
        "employee_id": employee["id"],
        "integrations": {}
    }

    # Check Google integration status
    if google_oauth:
        try:
            google_status = await google_oauth.check_oauth_status(employee["id"])
            status["integrations"]["google"] = google_status
        except Exception as e:
            status["integrations"]["google"] = {
                "connected": False,
                "error": str(e)
            }
    else:
        status["integrations"]["google"] = {
            "connected": False,
            "error": "Google integration service unavailable"
        }

    # Add Composio status (placeholder for now)
    status["integrations"]["composio"] = {
        "connected": False,
        "note": "Composio integration coming soon"
    }

    return status

# OAuth Token Management (for MCP integration)
@app.get("/employee/oauth-tokens/{provider}")
async def get_employee_oauth_tokens(
    provider: str,
    employee = Depends(validate_employee_token)
):
    """Get OAuth tokens for employee (for MCP integration)"""
    if provider.lower() != "google":
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported provider: {provider}"
        )

    if not google_oauth:
        raise HTTPException(
            status_code=503,
            detail="Google integration service unavailable"
        )

    try:
        # Get tokens from database
        result = supabase.table('oauth_tokens').select('*').eq('employee_id', employee["id"]).eq('provider', 'google').execute()

        if not result.data:
            raise HTTPException(
                status_code=404,
                detail="No OAuth tokens found for this provider"
            )

        token_data = result.data[0]

        # Check if tokens need refresh
        if token_data.get('expires_at'):
            expires_at = datetime.fromisoformat(token_data['expires_at'].replace('Z', '+00:00'))
            if expires_at <= datetime.now(expires_at.tzinfo):
                # Refresh tokens
                refresh_result = await google_oauth.refresh_tokens(employee["id"])
                if refresh_result.get("success"):
                    # Get updated tokens
                    result = supabase.table('oauth_tokens').select('*').eq('employee_id', employee["id"]).eq('provider', 'google').execute()
                    token_data = result.data[0] if result.data else token_data

        return {
            "provider": provider,
            "access_token": token_data.get('access_token'),
            "refresh_token": token_data.get('refresh_token'),
            "expires_at": token_data.get('expires_at'),
            "scope": token_data.get('scope'),
            "token_type": token_data.get('token_type', 'Bearer')
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve tokens: {str(e)}"
        )

# Admin endpoints
@app.post("/admin/retry-integration-init")
async def retry_integration_initialization():
    """Retry integration system initialization (admin endpoint)"""
    global supabase, google_oauth, integration_initialized

    # Try to reinitialize Supabase
    supabase = initialize_supabase()

    # Try to reinitialize integration systems
    integration_initialized = initialize_integration_systems()

    return {
        "supabase_connected": supabase is not None,
        "integration_systems_initialized": integration_initialized,
        "google_oauth_available": google_oauth is not None,
        "message": "Reinitialization attempt completed"
    }

# Composio Integration Endpoints (merged from composio-service)
class ComposioConnectRequest(BaseModel):
    redirect_url: Optional[str] = None
    entity_id: Optional[str] = None

class ComposioExecuteRequest(BaseModel):
    app_name: str
    action_name: str
    parameters: Dict[str, Any] = Field(default_factory=dict)

@app.post("/integrations/composio/entities")
async def create_composio_entity(employee = Depends(validate_employee_token)):
    """Create or retrieve Composio entity for employee (white-labeled)"""
    try:
        # For now, return a mock entity ID based on employee ID
        # In a full implementation, this would create a Composio entity
        entity_id = f"uru_entity_{employee['id']}"

        return {
            "success": True,
            "entity_id": entity_id,
            "message": "Entity ready for app connections"
        }

    except Exception as e:
        logger.error(f"Entity creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to prepare workspace for app connections"
        )

@app.post("/integrations/composio/connect/{app_name}")
async def connect_composio_app(
    app_name: str,
    request: ComposioConnectRequest,
    employee = Depends(validate_employee_token)
):
    """Connect Composio app for current employee (white-labeled)"""
    try:
        # This is a placeholder for Composio integration
        # In a full implementation, this would:
        # 1. Create or get Composio entity for employee
        # 2. Initiate OAuth flow for the specific app
        # 3. Store connection details in database

        logger.info(f"Connecting {app_name} for employee {employee['id']}")

        # For now, return a placeholder response
        return {
            "success": True,
            "message": f"Composio {app_name} connection initiated",
            "app_name": app_name,
            "employee_id": employee["id"],
            "note": "Composio integration implementation in progress"
        }

    except Exception as e:
        logger.error(f"Failed to connect {app_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to connect {app_name}: {str(e)}"
        )

@app.get("/integrations/composio/connections")
async def list_composio_connections(employee = Depends(validate_employee_token)):
    """List employee's Composio app connections (white-labeled)"""
    try:
        # Query the oauth_tokens table for composio providers
        result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).like("provider", "composio_%").execute()

        # Transform to match expected frontend format
        connections = []
        for token in result.data:
            # Extract app name from provider (composio_gmail -> gmail)
            app_name = token["provider"].replace("composio_", "")

            connections.append({
                "app_name": app_name,
                "status": "active" if token.get("expires_at") else "inactive",
                "connected_at": token.get("created_at"),
                "last_updated": token.get("updated_at")
            })

        return {
            "success": True,
            "connections": connections,
            "total": len(connections)
        }

    except Exception as e:
        logger.error(f"Failed to list connections: {e}")
        # Return empty connections list instead of error to match oauth-service behavior
        return {
            "success": True,
            "connections": [],
            "total": 0,
            "message": "Personal productivity tools are temporarily unavailable."
        }

@app.delete("/integrations/composio/connections/{app_name}")
async def disconnect_composio_app(
    app_name: str,
    employee = Depends(validate_employee_token)
):
    """Disconnect Composio app (white-labeled)"""
    try:
        # This is a placeholder for Composio integration
        # In a full implementation, this would:
        # 1. Remove OAuth tokens from Composio
        # 2. Delete connection record from database
        # 3. Clean up any stored credentials

        logger.info(f"Disconnecting {app_name} for employee {employee['id']}")

        return {
            "success": True,
            "message": f"Composio {app_name} disconnected",
            "app_name": app_name,
            "employee_id": employee["id"]
        }

    except Exception as e:
        logger.error(f"Failed to disconnect {app_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to disconnect {app_name}: {str(e)}"
        )

@app.post("/integrations/composio/execute")
async def execute_composio_tool(
    request: ComposioExecuteRequest,
    employee = Depends(validate_employee_token)
):
    """Execute Composio MCP tool (white-labeled)"""
    try:
        # This is a placeholder for Composio tool execution
        # In a full implementation, this would:
        # 1. Validate employee has connection to the app
        # 2. Retrieve stored OAuth tokens
        # 3. Execute the tool via Composio API
        # 4. Return the result

        logger.info(f"Executing {request.app_name}.{request.action_name} for employee {employee['id']}")

        return {
            "success": True,
            "result": {
                "message": f"Tool {request.action_name} executed successfully",
                "app_name": request.app_name,
                "action_name": request.action_name,
                "parameters": request.parameters
            },
            "note": "Composio tool execution implementation in progress"
        }

    except Exception as e:
        logger.error(f"Failed to execute tool: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Tool execution failed: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
