# Production Deployment Checklist - Uru Workspace Platform

## 🚨 CRITICAL: API Keys Configuration

Before deploying to production, you MUST replace the following placeholder values in your Elestio environment variables:

### Required API Keys

1. **Composio API Key**
   - Current: `YOUR_ACTUAL_COMPOSIO_API_KEY_HERE`
   - Get from: https://app.composio.dev/settings
   - Environment Variable: `URU_COMPOSIO_API_KEY`

2. **OpenAI API Key**
   - Current: `YOUR_ACTUAL_OPENAI_API_KEY_HERE`
   - Get from: https://platform.openai.com/api-keys
   - Environment Variable: `OPENAI_API_KEY`

### How to Update in Elestio

1. Log into your Elestio dashboard
2. Navigate to your uru-workspace-platform service
3. Go to "Environment Variables" section
4. Update the placeholder values with your actual API keys
5. Restart the service for changes to take effect

## 🔒 Security Checklist

### Multi-tenant Isolation ✅
- [x] Workspace validation middleware implemented
- [x] Employee access restricted to their workspace
- [x] Composio entities include workspace ID isolation
- [x] OAuth tokens scoped to employee and workspace

### Authentication & Authorization ✅
- [x] JWT token validation on all endpoints
- [x] Rate limiting per employee implemented
- [x] Workspace access validation on tool execution
- [x] Encrypted OAuth token storage

### Audit Logging ✅
- [x] Comprehensive audit logging system
- [x] All OAuth operations logged
- [x] Tool execution events tracked
- [x] Workspace-level event isolation

## 🏗️ Architecture Verification

### White-labeling ✅
- [x] No Composio branding exposed to users
- [x] All OAuth flows use uruenterprises.com domains
- [x] Backend-only Composio API usage
- [x] Custom service names and descriptions

### Database Schema ✅
- [x] Consolidated OAuth storage in oauth_tokens table
- [x] Migration from composio_connections completed
- [x] Audit logs table created
- [x] Proper foreign key constraints

### Service Communication ✅
- [x] Internal Docker network communication
- [x] Health checks on all services
- [x] Proper service dependencies
- [x] JWT token propagation

## 📊 Monitoring & Health Checks

### Service Health Endpoints
- Auth Service: `http://auth-service:8003/health`
- Integration Service: `http://integration-service:8002/health`
- Composio Service: `http://composio-service:8001/health`
- MCP Proxy: `http://mcp-proxy:3001/health`
- Frontend: `http://frontend:3000/api/health`

### Database Tables to Monitor
- `audit_logs` - System events and compliance
- `oauth_tokens` - OAuth connection status
- `employees` - User authentication
- `oauth_states` - OAuth flow security

## 🚀 Deployment Steps

### 1. Pre-deployment
- [ ] Update API keys in Elestio environment variables
- [ ] Verify all environment variables are set
- [ ] Run database migrations if needed
- [ ] Test OAuth flows in staging environment

### 2. Deployment
- [ ] Push code changes to repository
- [ ] Elestio will automatically rebuild and deploy
- [ ] Monitor service startup logs
- [ ] Verify all health checks pass

### 3. Post-deployment Verification
- [ ] Test user authentication flow
- [ ] Verify OAuth connections work
- [ ] Test tool execution with workspace isolation
- [ ] Check audit logs are being created
- [ ] Verify rate limiting is working

## 🔧 Troubleshooting

### Common Issues

1. **Service Won't Start**
   - Check environment variables are set correctly
   - Verify API keys are valid
   - Check Docker container logs in Elestio

2. **OAuth Failures**
   - Verify Google OAuth credentials
   - Check redirect URIs match production domains
   - Ensure workspace validation is not blocking access

3. **Tool Execution Errors**
   - Verify Composio API key is valid
   - Check OAuth tokens are not expired
   - Ensure workspace isolation is working correctly

### Log Locations
- Elestio Dashboard > Service > Logs
- Audit logs in `audit_logs` database table
- Application logs in service containers

## 📈 Performance Monitoring

### Key Metrics to Track
- OAuth token refresh success rate
- Tool execution response times
- Rate limiting trigger frequency
- Audit log volume
- Service health check status

### Recommended Alerts
- Failed OAuth token refreshes
- High rate limiting triggers
- Service health check failures
- Database connection issues
- Audit log creation failures

## 🔄 Maintenance Tasks

### Daily
- Monitor service health
- Check for failed OAuth refreshes
- Review audit logs for anomalies

### Weekly
- Review rate limiting metrics
- Check OAuth token expiration dates
- Verify backup systems

### Monthly
- Clean up old audit logs (90+ days)
- Review and rotate API keys if needed
- Update dependencies and security patches

## 📞 Support Contacts

For deployment issues:
1. Check this checklist first
2. Review Elestio documentation
3. Check service logs for specific errors
4. Contact development team with specific error messages and logs
