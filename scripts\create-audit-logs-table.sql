-- ===========================================
-- AUDIT LOGS TABLE CREATION
-- Comprehensive audit logging for compliance and monitoring
-- ===========================================

-- Create audit_logs table for tracking all system events
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID NOT NULL,
  workspace_id UUID NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  event_details JSONB DEFAULT '{}',
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  service VARCHAR(50) NOT NULL,
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_audit_logs_employee_id ON audit_logs(employee_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_workspace_id ON audit_logs(workspace_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_success ON audit_logs(success);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_audit_logs_workspace_employee ON audit_logs(workspace_id, employee_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_workspace_event_type ON audit_logs(workspace_id, event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_employee_timestamp ON audit_logs(employee_id, timestamp DESC);

-- Add foreign key constraints
ALTER TABLE audit_logs 
ADD CONSTRAINT fk_audit_logs_employee_id 
FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE;

-- Add check constraints for data validation
ALTER TABLE audit_logs 
ADD CONSTRAINT chk_audit_logs_event_type_not_empty 
CHECK (length(trim(event_type)) > 0);

ALTER TABLE audit_logs 
ADD CONSTRAINT chk_audit_logs_service_not_empty 
CHECK (length(trim(service)) > 0);

-- Create RLS (Row Level Security) policies for multi-tenant isolation
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see audit logs from their own workspace
CREATE POLICY audit_logs_workspace_isolation ON audit_logs
  FOR ALL
  USING (
    workspace_id IN (
      SELECT workspace_id 
      FROM employees 
      WHERE id = auth.uid()
    )
  );

-- Policy: Service accounts can access all audit logs (for system operations)
CREATE POLICY audit_logs_service_access ON audit_logs
  FOR ALL
  TO service_role
  USING (true);

-- Create a view for common audit log queries
CREATE OR REPLACE VIEW audit_logs_summary AS
SELECT 
  workspace_id,
  event_type,
  service,
  success,
  COUNT(*) as event_count,
  MIN(timestamp) as first_occurrence,
  MAX(timestamp) as last_occurrence,
  COUNT(DISTINCT employee_id) as unique_employees
FROM audit_logs
GROUP BY workspace_id, event_type, service, success;

-- Grant appropriate permissions
GRANT SELECT ON audit_logs TO authenticated;
GRANT SELECT ON audit_logs_summary TO authenticated;
GRANT ALL ON audit_logs TO service_role;

-- Create function to clean up old audit logs (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM audit_logs 
  WHERE timestamp < NOW() - INTERVAL '1 day' * retention_days;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled job to run cleanup weekly (requires pg_cron extension)
-- SELECT cron.schedule('audit-logs-cleanup', '0 2 * * 0', 'SELECT cleanup_old_audit_logs(90);');

COMMENT ON TABLE audit_logs IS 'Comprehensive audit logging for all system events with workspace isolation';
COMMENT ON COLUMN audit_logs.event_type IS 'Type of event (e.g., tool_execution_started, oauth_connection_created)';
COMMENT ON COLUMN audit_logs.event_details IS 'JSON object containing event-specific details';
COMMENT ON COLUMN audit_logs.service IS 'Service that generated the audit log (e.g., composio-service, auth-service)';
COMMENT ON FUNCTION cleanup_old_audit_logs IS 'Removes audit logs older than specified retention period';
